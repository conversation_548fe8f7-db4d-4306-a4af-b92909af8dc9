--------- beginning of crash
--------- beginning of system
--------- beginning of main
22:19:31.469 VerityUtils              E  Failed to measure fs-verity, errno 95: /data/app/~~foquKP4khKbRsn4RKbU2hA==/com.byevape.app-JeZpId7JFiqfcU-3L2G9zw==/base.apk
22:19:31.643 VerityUtils              E  Failed to measure fs-verity, errno 95: /data/app/~~foquKP4khKbRsn4RKbU2hA==/com.byevape.app-JeZpId7JFiqfcU-3L2G9zw==/base.apk
---------------------------- PROCESS STARTED (548) for package com.byevape.app ----------------------------
22:19:31.844 Compatibil...geReporter  D  Compat change id reported: 171979766; UID 10282; state: ENABLED
22:19:31.855 ziparchive               W  Unable to open '/data/app/~~foquKP4khKbRsn4RKbU2hA==/com.byevape.app-JeZpId7JFiqfcU-3L2G9zw==/base.dm': No such file or directory
22:19:31.855 ziparchive               W  Unable to open '/data/app/~~foquKP4khKbRsn4RKbU2hA==/com.byevape.app-JeZpId7JFiqfcU-3L2G9zw==/base.dm': No such file or directory
22:19:31.947 nativeloader             D  Configuring classloader-namespace for other apk /data/app/~~foquKP4khKbRsn4RKbU2hA==/com.byevape.app-JeZpId7JFiqfcU-3L2G9zw==/base.apk. target_sdk_version=35, uses_libraries=, library_path=/data/app/~~foquKP4khKbRsn4RKbU2hA==/com.byevape.app-JeZpId7JFiqfcU-3L2G9zw==/lib/arm64, permitted_path=/data:/mnt/expand:/data/user/0/com.byevape.app
22:19:31.962 GraphicsEnvironment      V  ANGLE Developer option for 'com.byevape.app' set to: 'default'
22:19:31.963 GraphicsEnvironment      V  ANGLE GameManagerService for com.byevape.app: false
22:19:31.963 GraphicsEnvironment      V  App is not on the allowlist for updatable production driver.
22:19:31.968 NetworkSecurityConfig    D  No Network Security Config specified, using platform default
22:19:31.979 NetworkSecurityConfig    D  No Network Security Config specified, using platform default
22:19:32.025 OpenGLRenderer           D  RenderThread::requireGlContext()
22:19:32.025 AdrenoGLES-0             I  QUALCOMM build                   : 193b2ee, I593c16c433
                                         Build Date                       : 10/07/21
                                         OpenGL ES Shader Compiler Version: EV031.32.02.10
                                         Local Branch                     : Test-lib-**********
                                         Remote Branch                    : 
                                         Remote Branch                    : 
                                         Reconstruct Branch               : 
22:19:32.025 AdrenoGLES-0             I  Build Config                     : S P 10.0.5 AArch64
22:19:32.025 AdrenoGLES-0             I  Driver Path                      : /vendor/lib64/egl/libGLESv2_adreno.so
22:19:32.031 AppCompatDelegate        D  Checking for metadata for AppLocalesMetadataHolderService : Service not found
22:19:32.034 AdrenoGLES-0             I  PFP: 0x016dd093, ME: 0x00000000
22:19:32.050 OpenGLRenderer           D  RenderThread::setGrContext()
22:19:32.068 DecorView                I  [INFO] isPopOver=false config=true
22:19:32.068 DecorView                I  updateCaptionType: isFloating=false isApplication=true hasWindowDecorCaption=false this=DecorView@2314e72[]
22:19:32.068 DecorView                D  setCaptionType = 0, this = DecorView@2314e72[]
22:19:32.073 DecorView                I  getCurrentDensityDpi: from real metrics. densityDpi=480 msg=resources_loaded
22:19:32.074 DecorView                I  setWindowBackground: isPopOver=false color=ff303030 d=android.graphics.drawable.ColorDrawable@b69d740
22:19:32.081 com.byevape.app          W  Accessing hidden method Landroid/view/ViewGroup;->makeOptionalFitsSystemWindows()V (unsupported, reflection, allowed)
22:19:32.090 WebViewFactory           I  Loading com.google.android.webview version 137.0.7151.89 (code 715108933)
22:19:32.093 ziparchive               W  Unable to open '/data/app/~~yE2qouejuzyBFkTRrMXViA==/com.google.android.trichromelibrary_715108933-9BLNfAUPpqktfpLPi_6ySA==/base.dm': No such file or directory
22:19:32.093 ziparchive               W  Unable to open '/data/app/~~yE2qouejuzyBFkTRrMXViA==/com.google.android.trichromelibrary_715108933-9BLNfAUPpqktfpLPi_6ySA==/base.dm': No such file or directory
22:19:32.093 com.byevape.app          W  Entry not found
22:19:32.094 nativeloader             D  Configuring classloader-namespace for other apk /data/app/~~yE2qouejuzyBFkTRrMXViA==/com.google.android.trichromelibrary_715108933-9BLNfAUPpqktfpLPi_6ySA==/base.apk. target_sdk_version=35, uses_libraries=ALL, library_path=/data/app/~~QZG-bSmhDq2-97vZWYidZQ==/com.google.android.webview-ChyLnFKpMoAQLjdZf-2kKQ==/lib/arm64:/data/app/~~QZG-bSmhDq2-97vZWYidZQ==/com.google.android.webview-ChyLnFKpMoAQLjdZf-2kKQ==/base.apk!/lib/arm64-v8a:/data/app/~~yE2qouejuzyBFkTRrMXViA==/com.google.android.trichromelibrary_715108933-9BLNfAUPpqktfpLPi_6ySA==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand
22:19:32.094 nativeloader             D  Extending system_exposed_libraries: libhumantracking.arcsoft.so:libPortraitDistortionCorrection.arcsoft.so:libPortraitDistortionCorrectionCali.arcsoft.so:libface_landmark.arcsoft.so:libFacialStickerEngine.arcsoft.so:libveengine.arcsoft.so:lib_pet_detection.arcsoft.so:libhigh_res.arcsoft.so:libimage_enhancement.arcsoft.so:liblow_light_hdr.arcsoft.so:libhigh_dynamic_range.arcsoft.so:libsuperresolution.arcsoft.so:libobjectcapture.arcsoft.so:libobjectcapture_jni.arcsoft.so:libobjectcapture_jni.arcsoft.so:libFacialAttributeDetection.arcsoft.so:libBeauty_v4.camera.samsung.so:libexifa.camera.samsung.so:libjpega.camera.samsung.so:libOpenCv.camera.samsung.so:libVideoClassifier.camera.samsung.so:libtensorflowLite.dynamic_viewing.camera.samsung.so:libImageScreener.camera.samsung.so:libMyFilter.camera.samsung.so:libtflite2.myfilters.camera.samsung.so:libHIDTSnapJNI.camera.samsung.so:libSmartScan.camera.samsung.so:libRectify.camera.samsung.so:libDocRectifyWrapper.camera.samsung.so:libUltraWideDistortionCorrection.camera.
22:19:32.097 nativeloader             D  Configuring classloader-namespace for other apk /data/app/~~QZG-bSmhDq2-97vZWYidZQ==/com.google.android.webview-ChyLnFKpMoAQLjdZf-2kKQ==/base.apk. target_sdk_version=35, uses_libraries=, library_path=/data/app/~~QZG-bSmhDq2-97vZWYidZQ==/com.google.android.webview-ChyLnFKpMoAQLjdZf-2kKQ==/lib/arm64:/data/app/~~QZG-bSmhDq2-97vZWYidZQ==/com.google.android.webview-ChyLnFKpMoAQLjdZf-2kKQ==/base.apk!/lib/arm64-v8a:/data/app/~~yE2qouejuzyBFkTRrMXViA==/com.google.android.trichromelibrary_715108933-9BLNfAUPpqktfpLPi_6ySA==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand
22:19:32.114 cr_WVCFactoryProvider    I  version=137.0.7151.89 (715108933) minSdkVersion=29 isBundle=false multiprocess=true packageId=127
22:19:32.129 chromium                 I  [0625/221932.128674:INFO:android_webview/browser/variations/variations_seed_loader.cc:67] Failed to open file for reading.: No such file or directory (2)
22:19:32.140 cr_LibraryLoader         I  Successfully loaded native library
22:19:32.141 cr_CachingUmaRecorder    I  Flushed 7 samples from 7 histograms, 0 samples were dropped.
22:19:32.144 cr_CombinedPProvider     I  #registerProvider() provider:WV.d9@8f851ff isPolicyCacheEnabled:false policyProvidersSize:0
22:19:32.145 cr_PolicyProvider        I  #setManagerAndSource() 0
22:19:32.161 cr_CombinedPProvider     I  #linkNativeInternal() 1
22:19:32.161 Compatibil...geReporter  D  Compat change id reported: 183155436; UID 10282; state: ENABLED
22:19:32.163 cr_AppResProvider        I  #getApplicationRestrictionsFromUserManager() Bundle[EMPTY_PARCEL]
22:19:32.163 cr_PolicyProvider        I  #notifySettingsAvailable() 0
22:19:32.163 cr_CombinedPProvider     I  #onSettingsAvailable() 0
22:19:32.163 cr_CombinedPProvider     I  #flushPolicies()
22:19:32.192 chromium                 W  [WARNING:net/dns/dns_config_service_android.cc:69] Failed to read DnsConfig.
22:19:32.256 Compatibil...geReporter  D  Compat change id reported: 214741472; UID 10282; state: ENABLED
22:19:32.260 Compatibil...geReporter  D  Compat change id reported: 171228096; UID 10282; state: ENABLED
22:19:32.284 Capacitor                D  Starting BridgeActivity
22:19:32.303 Capacitor                D  Registering plugin instance: CapacitorCookies
22:19:32.305 Capacitor                D  Registering plugin instance: WebView
22:19:32.306 Capacitor                D  Registering plugin instance: CapacitorHttp
22:19:32.307 Capacitor                D  Registering plugin instance: Camera
22:19:32.308 Capacitor                D  Registering plugin instance: SplashScreen
22:19:32.318 Capacitor                W  Unable to read file at path public/plugins
22:19:32.322 Capacitor                D  Loading app at https://localhost
22:19:32.365 cr_media                 W  BLUETOOTH_CONNECT permission is missing.
22:19:32.366 cr_media                 W  getBluetoothAdapter() requires BLUETOOTH permission
22:19:32.366 cr_media                 W  registerBluetoothIntentsIfNeeded: Requires BLUETOOTH permission
22:19:32.393 Capacitor                D  App started
22:19:32.396 Capacitor                D  App resumed
22:19:32.397 MSHandlerLifeCycle       I  check: return. pkg=com.byevape.app parent=null callers=com.android.internal.policy.DecorView.setVisibility:4412 android.app.ActivityThread.handleResumeActivity:5476 android.app.servertransaction.ResumeActivityItem.execute:54 android.app.servertransaction.ActivityTransactionItem.execute:45 android.app.servertransaction.TransactionExecutor.executeLifecycleState:176 
22:19:32.397 MSHandlerLifeCycle       I  removeMultiSplitHandler: no exist. decor=DecorView@2314e72[]
22:19:32.404 NativeCust...ncyManager  D  [NativeCFMS] BpCustomFrequencyManager::BpCustomFrequencyManager()
22:19:32.411 InsetsController         D  onStateChanged: InsetsState: {mDisplayFrame=Rect(0, 0 - 1080, 2400), mDisplayCutout=DisplayCutout{insets=Rect(0, 88 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 0 - 0, 0), Rect(512, 0 - 568, 88), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2400 physicalDisplayWidth=1080 physicalDisplayHeight=2400 density={3.0} cutoutSpec={M 0,0 M 0,10.71963616907435 a 9.280363830925644,9.280363830925644 0 1,0 0,18.56072766185129 a 9.280363830925644,9.280363830925644 0 1,0 0,-18.56072766185129 Z @dp} rotation={0} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=96, center=Point(96, 96)}, RoundedCorner{position=TopRight, radius=96, center=Point(984, 96)}, RoundedCorner{position=BottomRight, radius=96, center=Point(984, 2304)}, RoundedCorner{position=BottomLeft, radius=96, center=Point(96, 2304)}]}  mRoundedCornerFrame=Rect(0, 0 - 1080, 2400), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(948, 0 - 1080, 88) rotation=0}, mSources= { InsetsSource: {mType=ITYPE_STATUS_BAR, mFrame=[0,0][1080,88], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_NAVIGATION_BAR, mFrame=[0,2256][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_LEFT_GESTURES, mFrame=[0,0][0,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_RIGHT_GESTURES, mFrame=[1080,0][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_TOP_MANDATORY_GESTURES, mFrame=[0,0][1080,124], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_BOTTOM_MANDATORY_GESTURES, mFrame=[0,2256][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_LEFT_DISPLAY_CUTOUT, mFrame=[0,0][-100000,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_TOP_DISPLAY_CUTOUT, mFrame=[0,0][1080,88], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_RIGHT_DISPLAY_CUTOUT, mFrame=[100000,0][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_BOTTOM_DISPLAY_CUTOUT, mFrame=[0,100000][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_TOP_TAPPABLE_ELEMENT, mFrame=[0,0][1080,88], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_BOTTOM_TAPPABLE_ELEMENT, mFrame=[0,2256][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false} } host=com.byevape.app/com.byevape.app.MainActivity from=android.view.ViewRootImpl.setView:1732
22:19:32.412 ViewRootIm...nActivity]  I  setView = com.android.internal.policy.DecorView@2314e72 TM=true
22:19:32.414 MSHandlerLifeCycle       I  removeMultiSplitHandler: no exist. decor=DecorView@2314e72[MainActivity]
22:19:32.417 Capacitor                D  Handling local request: https://localhost/
22:19:32.420 Choreographer            I  Skipped 49 frames!  The application may be doing too much work on its main thread.
22:19:32.426 Compatibil...geReporter  D  Compat change id reported: 193247900; UID 10282; state: ENABLED
22:19:32.432 ViewRootIm...nActivity]  I  performTraversals params={(0,0)(fillxfill) sim={adjust=resize forwardNavigation} layoutInDisplayCutoutMode=shortEdges ty=BASE_APPLICATION wanim=0x1030309
                                           fl=81810100
                                           pfl=12020040
                                           bhv=DEFAULT
                                           fitSides= naviIconColor=0}
22:19:32.432 ViewRootIm...nActivity]  I  performTraversals mFirst=true windowShouldResize=true viewVisibilityChanged=false mForceNextWindowRelayout=false params={(0,0)(fillxfill) sim={adjust=resize forwardNavigation} layoutInDisplayCutoutMode=shortEdges ty=BASE_APPLICATION wanim=0x1030309
                                           fl=81810100
                                           pfl=12020040
                                           bhv=DEFAULT
                                           fitSides= naviIconColor=0}
22:19:32.440 ViewRootIm...nActivity]  I  updateBlastSurfaceIfNeeded mBlastBufferQueue=null isSameSurfaceControl=false
22:19:32.440 BufferQueueProducer      E  Unable to open libpenguin.so: dlopen failed: library "libpenguin.so" not found.
22:19:32.441 BLASTBufferQueue         I  new BLASTBufferQueue, mName= ViewRootImpl@18271c3[MainActivity] mNativeObject= 0xb400007e3d0e1b00 sc.mNativeObject= 0xb400007dcce1f740 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:2909 android.view.ViewRootImpl.relayoutWindow:9847 android.view.ViewRootImpl.performTraversals:3884 android.view.ViewRootImpl.doTraversal:3116 android.view.ViewRootImpl$TraversalRunnable.run:10885 android.view.Choreographer$CallbackRecord.run:1301 android.view.Choreographer$CallbackRecord.run:1309 android.view.Choreographer.doCallbacks:923 android.view.Choreographer.doFrame:852 android.view.Choreographer$FrameDisplayEventReceiver.run:1283 
22:19:32.441 BLASTBufferQueue         I  update, w= 1080 h= 2400 mName = ViewRootImpl@18271c3[MainActivity] mNativeObject= 0xb400007e3d0e1b00 sc.mNativeObject= 0xb400007dcce1f740 format= -1 caller= android.graphics.BLASTBufferQueue.<init>:84 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:2909 android.view.ViewRootImpl.relayoutWindow:9847 android.view.ViewRootImpl.performTraversals:3884 android.view.ViewRootImpl.doTraversal:3116 android.view.ViewRootImpl$TraversalRunnable.run:10885 
22:19:32.441 ViewRootIm...nActivity]  I  Relayout returned: old=(0,0,1080,2400) new=(0,0,1080,2400) req=(1080,2400)0 dur=7 res=0x3 s={true 0xb400007e25e32800} ch=true seqId=0
22:19:32.442 ViewRootIm...nActivity]  I  mThreadedRenderer.initialize() mSurface={isValid=true 0xb400007e25e32800} hwInitialized=true
22:19:32.445 OpenGLRenderer           D  eglCreateWindowSurface
22:19:32.445 ViewRootIm...nActivity]  I  reportNextDraw android.view.ViewRootImpl.performTraversals:4438 android.view.ViewRootImpl.doTraversal:3116 android.view.ViewRootImpl$TraversalRunnable.run:10885 android.view.Choreographer$CallbackRecord.run:1301 android.view.Choreographer$CallbackRecord.run:1309 
22:19:32.481 Capacitor                D  Handling local request: https://localhost/assets/index-D_l-cuzP.css
22:19:32.482 Capacitor                D  Handling local request: https://localhost/assets/index-BsaFkeDn.js
22:19:32.527 AdrenoVK-0               I  ===== BEGIN DUMP OF OVERRIDDEN SETTINGS =====
22:19:32.527 AdrenoVK-0               I  ===== END DUMP OF OVERRIDDEN SETTINGS =====
22:19:32.527 AdrenoVK-0               I  QUALCOMM build          : 193b2ee, I593c16c433
                                         Build Date              : 10/07/21
                                         Shader Compiler Version : EV031.32.02.10
                                         Local Branch            : Test-lib-**********
                                         Remote Branch           : 
                                         Remote Branch           : 
                                         Reconstruct Branch      : 
22:19:32.527 AdrenoVK-0               I  Build Config            : S P 10.0.5 AArch64
22:19:32.527 AdrenoVK-0               I  Driver Path             : /vendor/lib64/hw/vulkan.adreno.so
22:19:32.573 CameraManagerGlobal      I  Connecting to camera service
22:19:32.576 VendorTagDescriptor      D  addVendorDescriptor: vendor tag id 14172875900359437128 added
22:19:32.579 CameraManager            I  registerAvailabilityCallback: Is device callback = false
22:19:32.579 CameraManagerGlobal      I  postSingleUpdate device: camera id 0 status STATUS_PRESENT
22:19:32.579 CameraManagerGlobal      I  postSingleUpdate device: camera id 1 status STATUS_PRESENT
22:19:32.579 CameraManagerGlobal      I  postSingleUpdate device: camera id 2 status STATUS_PRESENT
22:19:32.580 CameraManagerGlobal      I  postSingleUpdate device: camera id 3 status STATUS_PRESENT
22:19:32.580 CameraManagerGlobal      I  Camera 0 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
22:19:32.581 CameraManagerGlobal      I  Camera 1 facing CAMERA_FACING_FRONT state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
22:19:32.581 CameraManagerGlobal      I  Camera 2 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
22:19:32.582 CameraManagerGlobal      I  Camera 20 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client com.sec.android.app.camera API Level 2User Id 0
22:19:32.582 CameraManagerGlobal      I  Camera 21 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
22:19:32.582 CameraManagerGlobal      I  Camera 23 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
22:19:32.582 CameraManagerGlobal      I  Camera 3 facing CAMERA_FACING_FRONT state now CAMERA_STATE_CLOSED for client com.samsung.adaptivebrightnessgo API Level 2User Id 0
22:19:32.582 CameraManagerGlobal      I  Camera 4 facing CAMERA_FACING_FRONT state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
22:19:32.583 CameraManagerGlobal      I  Camera 40 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
22:19:32.583 CameraManagerGlobal      I  Camera 41 facing CAMERA_FACING_FRONT state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
22:19:32.583 CameraManagerGlobal      I  Camera 52 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
22:19:32.615 VideoCapabilities        W  Unsupported mime image/vnd.android.heic
22:19:32.635 AudioCapabilities        W  Unsupported mime audio/x-ape
22:19:32.636 AudioCapabilities        W  Unsupported mime audio/x-ima
22:19:32.636 AudioCapabilities        W  Unsupported mime audio/mpeg-L1
22:19:32.636 AudioCapabilities        W  Unsupported mime audio/mpeg-L2
22:19:32.636 VideoCapabilities        W  Unsupported mime video/mp43
22:19:32.637 VideoCapabilities        W  Unsupported mime video/wvc1
22:19:32.637 VideoCapabilities        W  Unsupported mime video/x-ms-wmv
22:19:32.637 AudioCapabilities        W  Unsupported mime audio/x-ms-wma
22:19:32.638 VideoCapabilities        W  Unsupported mime video/x-ms-wmv7
22:19:32.638 VideoCapabilities        W  Unsupported mime video/x-ms-wmv8
22:19:32.912 com.byevape.app          I  Compiler allocated 6133KB to compile void android.view.ViewRootImpl.performTraversals()
22:19:33.685 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 44 - Msg: 📝 Logger initialized with level: debug
22:19:33.688 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 120 - Msg: ℹ️ [INFO] 🏗️ AppState constructor called [object Object]
22:19:33.692 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 5147 - Msg: 🚀 ByeVape main entry point loaded
22:19:33.693 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 5114 - Msg: 🚀 Initializing ByeVape app with modular architecture...
22:19:33.694 Capacitor                D  Handling local request: https://localhost/assets/index-B7lODB1_.js
22:19:33.694 Capacitor/Console        E  File: https://localhost/assets/index-BsaFkeDn.js - Line 5133 - Msg: ❌ Critical error during modular initialization: ReferenceError: showSplashScreen is not defined
22:19:33.694 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 5134 - Msg: 🔄 Falling back to legacy initialization...
22:19:33.694 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 2724 - Msg: Initializing ByeVape AppState...
22:19:33.695 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 2801 - Msg: 🔄 User data migrated to new format
22:19:33.695 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 2899 - Msg: 📦 Data backup created
22:19:33.696 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 2845 - Msg: ✅ User data saved to localStorage
22:19:33.696 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 2743 - Msg: User data loaded successfully
22:19:33.696 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 2752 - Msg: No daily history found
22:19:33.696 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 3521 - Msg: 🚀 Starting app - isFirstLaunch: false, startDate: 2025-06-09T20:38:44.495Z
22:19:33.696 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 3526 - Msg: 🔄 Returning user - showing tracker
22:19:33.696 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 4545 - Msg: 🔄 Checking for daily reset...
22:19:33.697 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 4553 - Msg: 📅 Last reset: Sat Jun 21 2025, Today: Wed Jun 25 2025, Days since start: 16
22:19:33.697 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 4554 - Msg: 📅 ISO dates - Last: null, Today: 2025-06-25
22:19:33.698 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 4561 - Msg: 🔄 Performing daily reset...
22:19:33.698 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 4660 - Msg: 🔄 Applying progressive reduction...
22:19:33.698 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 4672 - Msg: 📊 Day 16 of 90 (relaxed mode)
22:19:33.698 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 4691 - Msg: 📉 Week 3/13: 14.0% reduction
22:19:33.699 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 4708 - Msg: ✅ Breaks reduced: 18 → 17
22:19:33.699 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 4579 - Msg: 🔄 Break mode reset - Day 16, breaks: 17
22:19:33.699 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 3393 - Msg: 🎯 Daily puff target updated: 170 puffs
22:19:33.699 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 2899 - Msg: 📦 Data backup created
22:19:33.700 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 2845 - Msg: ✅ User data saved to localStorage
22:19:33.700 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 4597 - Msg: ✅ Daily reset complete - Day 16, 17 breaks available
22:19:33.700 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 3554 - Msg: 🔄 showPage called with pageId: tracker
22:19:33.700 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 3556 - Msg: 📄 Found 4 pages to hide
22:19:33.700 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 3558 - Msg:    Hiding page 0: onboarding-page
22:19:33.700 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 3558 - Msg:    Hiding page 1: tracker-page
22:19:33.701 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 3558 - Msg:    Hiding page 2: statistics-page
22:19:33.701 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 3558 - Msg:    Hiding page 3: settings-page
22:19:33.701 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 3562 - Msg: 🎯 Target page element: [object HTMLDivElement]
22:19:33.701 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 3565 - Msg: ✅ Showing page: tracker-page
22:19:33.701 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 3567 - Msg: 📊 Page styles - display: block, visibility: visible, opacity: 1
22:19:33.701 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 3577 - Msg: 🧭 Bottom navigation element: [object HTMLElement]
22:19:33.702 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 3586 - Msg: 🔓 Navigation shown for tracker
22:19:33.702 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 3589 - Msg: ✨ showPage completed for: tracker
22:19:33.702 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 3770 - Msg: 🔄 Updating tracker data...
22:19:33.702 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 3794 - Msg: ⏰ Showing break timer interface
22:19:33.702 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 3835 - Msg: ⏰ Updating break tracker...
22:19:33.702 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 3782 - Msg: ✅ Tracker updated - Day 16
22:19:33.703 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 4148 - Msg: 🔄 Starting countdown timer...
22:19:33.703 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 4151 - Msg: ⏰ Minutes between breaks: 63.5 (17 breaks per day)
22:19:33.703 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 4184 - Msg: 🎯 First break available in 1 minute
22:19:33.728 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 4172 - Msg: ✅ Timer started - next break at 22:20
22:19:33.728 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 2729 - Msg: ByeVape AppState initialized successfully
22:19:33.729 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 5075 - Msg: ByeVape app initialized successfully
22:19:33.729 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 5105 - Msg: ✅ Global functions attached to window
22:19:33.729 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 5043 - Msg: 🔄 Hiding splash screen...
22:19:33.729 Capacitor/Console        I  File: https://localhost/assets/index-BsaFkeDn.js - Line 5048 - Msg: ℹ️ Running in browser - no splash screen to hide
22:19:33.748 Capacitor                D  Handling local request: https://localhost/assets/favicon-UvKAN7pv.ico
22:19:38.175 ProfileInstaller         D  Installing profile for com.byevape.app